import React from 'react';
import { Card, Row, Col, Form, Input, TimePicker, Select, InputNumber } from 'antd';
import dayjs from 'dayjs';
import { WEEKDAY_OPTIONS } from '../../../types/task';
import { TaskGroupSelect } from '../../common/TaskGroupSelect';

const { Option } = Select;

interface BasicInfoFormProps {
  /** 表单实例 */
  form: any;
}

/**
 * 基本信息表单组件
 * 包含任务名称、分组、时间、状态、星期、频率等基本配置
 */
const BasicInfoForm: React.FC<BasicInfoFormProps> = ({ form }) => {
  return (
    <Card title='任务执行配置' className='mb-4'>
      <Row gutter={16} className='mb-4'>
        <Col span={12}>
          <Form.Item
            label='任务名称'
            name='name'
            rules={[
              {
                required: true,
                message: '请输入任务名称',
              },
            ]}
          >
            <Input 
              placeholder='请输入任务名称' 
              autoComplete='off' 
              autoCorrect='off' 
              autoCapitalize='off' 
              spellCheck={false} 
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label='任务分组'
            name='group_name'
            rules={[
              {
                required: true,
                message: '请选择任务分组',
              },
            ]}
          >
            <TaskGroupSelect placeholder='请选择任务分组' dynamicSearch={true} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16} className='mb-4'>
        <Col span={8}>
          <Form.Item
            label='开始时间'
            name='start_time'
            rules={[
              {
                required: true,
                message: '请选择开始时间',
              },
            ]}
            getValueFromEvent={time => (time ? time.format('HH:mm:ss') : '')}
            getValueProps={value => ({
              value: value ? (typeof value === 'string' ? dayjs(value, 'HH:mm:ss') : value) : undefined,
            })}
          >
            <TimePicker
              placeholder='请选择开始时间'
              format='HH:mm:ss'
              style={{
                width: '100%',
              }}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label='结束时间'
            name='end_time'
            rules={[
              {
                required: true,
                message: '请选择结束时间',
              },
            ]}
            getValueFromEvent={time => (time ? time.format('HH:mm:ss') : '')}
            getValueProps={value => ({
              value: value ? (typeof value === 'string' ? dayjs(value, 'HH:mm:ss') : value) : undefined,
            })}
          >
            <TimePicker
              placeholder='请选择结束时间'
              format='HH:mm:ss'
              style={{
                width: '100%',
              }}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label='状态'
            name='status'
            rules={[
              {
                required: true,
                message: '请选择状态',
              },
            ]}
          >
            <Select placeholder='请选择状态'>
              <Option key='enabled' value='enabled'>
                启用
              </Option>
              <Option key='disabled' value='disabled'>
                禁用
              </Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16} className='mb-4'>
        <Col span={12}>
          <Form.Item
            label='星期'
            name='weekday'
            rules={[
              {
                required: true,
                message: '请选择星期',
              },
            ]}
          >
            <Select mode='multiple' placeholder='请选择星期'>
              {WEEKDAY_OPTIONS.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label='重试次数'
            name='retry_num'
            rules={[
              {
                required: true,
                message: '请输入重试次数',
              },
            ]}
          >
            <InputNumber
              placeholder='请输入重试次数'
              min={0}
              max={10}
              style={{
                width: '100%',
              }}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16} className='mb-4'>
        <Col span={12}>
          <Form.Item label='执行频率' required>
            <Input.Group compact>
              <Form.Item
                name={['frequency', 'value']}
                noStyle
                rules={[
                  {
                    required: true,
                    message: '请输入执行频率数值',
                  },
                ]}
              >
                <InputNumber
                  placeholder='数值'
                  min={1}
                  style={{
                    width: '70%',
                  }}
                />
              </Form.Item>
              <Form.Item
                name={['frequency', 'unit']}
                noStyle
                rules={[
                  {
                    required: true,
                    message: '请选择单位',
                  },
                ]}
              >
                <Select
                  placeholder='单位'
                  style={{
                    width: '30%',
                  }}
                >
                  <Option value='sec'>秒</Option>
                  <Option value='min'>分钟</Option>
                  <Option value='hour'>小时</Option>
                  <Option value='day'>天</Option>
                </Select>
              </Form.Item>
            </Input.Group>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label='重试间隔' required>
            <Input.Group compact>
              <Form.Item
                name={['retry_frequency', 'value']}
                noStyle
                rules={[
                  {
                    required: true,
                    message: '请输入重试间隔数值',
                  },
                ]}
              >
                <InputNumber
                  placeholder='数值'
                  min={1}
                  style={{
                    width: '70%',
                  }}
                />
              </Form.Item>
              <Form.Item
                name={['retry_frequency', 'unit']}
                noStyle
                rules={[
                  {
                    required: true,
                    message: '请选择单位',
                  },
                ]}
              >
                <Select
                  placeholder='单位'
                  style={{
                    width: '30%',
                  }}
                >
                  <Option value='sec'>秒</Option>
                  <Option value='min'>分钟</Option>
                  <Option value='hour'>小时</Option>
                </Select>
              </Form.Item>
            </Input.Group>
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );
};

export default BasicInfoForm;
