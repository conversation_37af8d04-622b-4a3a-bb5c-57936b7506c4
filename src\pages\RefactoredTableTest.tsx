import React from 'react';
import { App } from 'antd';
import { AntdTableRefactored } from '../components/AntdTable/index';

/**
 * 重构后的表格组件测试页面
 */
const RefactoredTableTest: React.FC = () => {
  return (
    <App>
      <div style={{ height: '100vh', padding: '20px' }}>
        <h1 style={{ marginBottom: '20px', textAlign: 'center' }}>
          重构后的 AntdTable 组件测试
        </h1>
        
        <div style={{ marginBottom: '20px', padding: '16px', backgroundColor: '#f0f9ff', borderRadius: '6px', border: '1px solid #0ea5e9' }}>
          <h3 style={{ color: '#0369a1', marginBottom: '12px' }}>🎉 重构完成！</h3>
          <div style={{ color: '#0c4a6e' }}>
            <p><strong>✅ 功能保持：</strong>所有原有功能完全保留，包括搜索、表格、分页、批量操作等</p>
            <p><strong>✅ 结构优化：</strong>将 951 行的单一组件拆分为多个小组件和 hooks</p>
            <p><strong>✅ 可维护性：</strong>代码结构清晰，职责分离，易于维护和扩展</p>
            <p><strong>✅ 可复用性：</strong>子组件和 hooks 可以在其他地方复用</p>
          </div>
        </div>

        <div style={{ 
          height: 'calc(100vh - 200px)', 
          border: '1px solid #d9d9d9', 
          borderRadius: '6px',
          backgroundColor: '#fff'
        }}>
          <AntdTableRefactored contentHeight={800} />
        </div>
      </div>
    </App>
  );
};

export default RefactoredTableTest;
