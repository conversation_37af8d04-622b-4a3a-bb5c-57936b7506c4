import React from 'react';
import { Card, Row, Col, Space, Button, Descriptions, Empty } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { OtherInfo } from '../../../types/task';

interface OtherInfoTabProps {
  /** 其他信息数据 */
  otherInfo: OtherInfo | null;
  /** 新增其他信息 */
  onAddOtherInfo: () => void;
  /** 编辑其他信息 */
  onEditOtherInfo: () => void;
  /** 删除其他信息 */
  onDeleteOtherInfo: () => void;
  /** 选择已有其他信息 */
  onSelectOtherInfo: () => void;
}

/**
 * 其他信息配置标签页组件
 * 包含其他信息的配置和管理
 */
const OtherInfoTab: React.FC<OtherInfoTabProps> = ({
  otherInfo,
  onAddOtherInfo,
  onEditOtherInfo,
  onDeleteOtherInfo,
  onSelectOtherInfo,
}) => {
  return (
    <Card title='其他信息配置' className='mb-4'>
      <Row gutter={16} className='mb-4'>
        <Col span={24}>
          <Space>
            <Button type='primary' icon={<PlusOutlined />} onClick={onAddOtherInfo}>
              新增其他信息
            </Button>
            <Button icon={<PlusOutlined />} onClick={onSelectOtherInfo}>
              选择已有其他信息
            </Button>
          </Space>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          {otherInfo ? (
            <Card
              size='small'
              title={`其他信息: ${otherInfo.name}`}
              extra={
                <Space>
                  <Button
                    type='text'
                    size='small'
                    icon={<EditOutlined />}
                    onClick={onEditOtherInfo}
                  >
                    编辑
                  </Button>
                  <Button
                    type='text'
                    size='small'
                    danger
                    icon={<DeleteOutlined />}
                    onClick={onDeleteOtherInfo}
                  >
                    删除
                  </Button>
                </Space>
              }
            >
              <Descriptions column={2} size='small'>
                <Descriptions.Item label='信息名称'>
                  {otherInfo.name}
                </Descriptions.Item>
                <Descriptions.Item label='信息类型'>
                  {otherInfo.type}
                </Descriptions.Item>
                {otherInfo.value && (
                  <Descriptions.Item label='信息值' span={2}>
                    {typeof otherInfo.value === 'object' 
                      ? JSON.stringify(otherInfo.value, null, 2)
                      : otherInfo.value
                    }
                  </Descriptions.Item>
                )}
                {otherInfo.description && (
                  <Descriptions.Item label='描述' span={2}>
                    {otherInfo.description}
                  </Descriptions.Item>
                )}
                {otherInfo.config && (
                  <Descriptions.Item label='配置信息' span={2}>
                    <pre style={{ 
                      background: '#f5f5f5', 
                      padding: '8px', 
                      borderRadius: '4px',
                      fontSize: '12px',
                      maxHeight: '200px',
                      overflow: 'auto'
                    }}>
                      {typeof otherInfo.config === 'object' 
                        ? JSON.stringify(otherInfo.config, null, 2)
                        : otherInfo.config
                      }
                    </pre>
                  </Descriptions.Item>
                )}
              </Descriptions>
            </Card>
          ) : (
            <Empty
              description='暂无其他信息配置'
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </Col>
      </Row>
    </Card>
  );
};

export default OtherInfoTab;
