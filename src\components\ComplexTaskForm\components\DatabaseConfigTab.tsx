import React from 'react';
import { Card, Row, Col, Space, Button, Descriptions, Empty } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { DBConnection } from '../../../types/task';

interface DatabaseConfigTabProps {
  /** 数据库连接数据 */
  dbConnection: DBConnection | null;
  /** 新增数据库连接 */
  onAddDbConnection: () => void;
  /** 编辑数据库连接 */
  onEditDbConnection: () => void;
  /** 删除数据库连接 */
  onDeleteDbConnection: () => void;
  /** 选择已有数据库连接 */
  onSelectDbConnection: () => void;
}

/**
 * 数据库连接配置标签页组件
 * 包含数据库连接的配置和管理
 */
const DatabaseConfigTab: React.FC<DatabaseConfigTabProps> = ({
  dbConnection,
  onAddDbConnection,
  onEditDbConnection,
  onDeleteDbConnection,
  onSelectDbConnection,
}) => {
  return (
    <Card title='数据库连接配置' className='mb-4'>
      <Row gutter={16} className='mb-4'>
        <Col span={24}>
          <Space>
            <Button type='primary' icon={<PlusOutlined />} onClick={onAddDbConnection}>
              新增数据库连接
            </Button>
            <Button icon={<PlusOutlined />} onClick={onSelectDbConnection}>
              选择已有数据库连接
            </Button>
          </Space>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          {dbConnection ? (
            <Card
              size='small'
              title={`数据库连接: ${dbConnection.name}`}
              extra={
                <Space>
                  <Button
                    type='text'
                    size='small'
                    icon={<EditOutlined />}
                    onClick={onEditDbConnection}
                  >
                    编辑
                  </Button>
                  <Button
                    type='text'
                    size='small'
                    danger
                    icon={<DeleteOutlined />}
                    onClick={onDeleteDbConnection}
                  >
                    删除
                  </Button>
                </Space>
              }
            >
              <Descriptions column={2} size='small'>
                <Descriptions.Item label='连接名称'>
                  {dbConnection.name}
                </Descriptions.Item>
                <Descriptions.Item label='数据库类型'>
                  {dbConnection.type}
                </Descriptions.Item>
                <Descriptions.Item label='主机地址'>
                  {dbConnection.host}
                </Descriptions.Item>
                <Descriptions.Item label='端口'>
                  {dbConnection.port}
                </Descriptions.Item>
                <Descriptions.Item label='数据库名'>
                  {dbConnection.database}
                </Descriptions.Item>
                <Descriptions.Item label='用户名'>
                  {dbConnection.username}
                </Descriptions.Item>
                <Descriptions.Item label='连接池大小'>
                  {dbConnection.pool_size || '默认'}
                </Descriptions.Item>
                <Descriptions.Item label='超时时间'>
                  {dbConnection.timeout || '默认'}秒
                </Descriptions.Item>
                {dbConnection.description && (
                  <Descriptions.Item label='描述' span={2}>
                    {dbConnection.description}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </Card>
          ) : (
            <Empty
              description='暂无数据库连接配置'
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </Col>
      </Row>
    </Card>
  );
};

export default DatabaseConfigTab;
