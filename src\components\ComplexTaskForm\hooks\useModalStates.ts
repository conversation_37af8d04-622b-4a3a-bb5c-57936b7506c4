import { useState } from 'react';

interface ModalState {
  visible: boolean;
  editingIndex?: number;
}

interface SelectModalState {
  visible: boolean;
  type: '' | 'alert' | 'alertSend' | 'dbConnection' | 'otherInfo';
}

interface UseModalStatesReturn {
  /** 告警Modal状态 */
  alertModal: ModalState;
  /** 设置告警Modal状态 */
  setAlertModal: React.Dispatch<React.SetStateAction<ModalState>>;
  /** 告警发送Modal状态 */
  alertSendModal: ModalState;
  /** 设置告警发送Modal状态 */
  setAlertSendModal: React.Dispatch<React.SetStateAction<ModalState>>;
  /** 数据库连接Modal状态 */
  dbConnectionModal: { visible: boolean };
  /** 设置数据库连接Modal状态 */
  setDbConnectionModal: React.Dispatch<React.SetStateAction<{ visible: boolean }>>;
  /** 其他信息Modal状态 */
  otherInfoModal: { visible: boolean };
  /** 设置其他信息Modal状态 */
  setOtherInfoModal: React.Dispatch<React.SetStateAction<{ visible: boolean }>>;
  /** 选择已有数据Modal状态 */
  selectModal: SelectModalState;
  /** 设置选择已有数据Modal状态 */
  setSelectModal: React.Dispatch<React.SetStateAction<SelectModalState>>;
}

/**
 * Modal状态管理Hook
 * 管理各种Modal的显示状态
 */
export const useModalStates = (): UseModalStatesReturn => {
  // Modal状态
  const [alertModal, setAlertModal] = useState<ModalState>({
    visible: false,
    editingIndex: -1,
  });

  // 告警发送Modal
  const [alertSendModal, setAlertSendModal] = useState<ModalState>({
    visible: false,
    editingIndex: -1,
  });

  // 数据库连接Modal
  const [dbConnectionModal, setDbConnectionModal] = useState({
    visible: false,
  });

  // 其他信息Modal
  const [otherInfoModal, setOtherInfoModal] = useState({ 
    visible: false 
  });

  // 选择已有数据Modal
  const [selectModal, setSelectModal] = useState<SelectModalState>({
    visible: false,
    type: '',
  });

  return {
    alertModal,
    setAlertModal,
    alertSendModal,
    setAlertSendModal,
    dbConnectionModal,
    setDbConnectionModal,
    otherInfoModal,
    setOtherInfoModal,
    selectModal,
    setSelectModal,
  };
};
