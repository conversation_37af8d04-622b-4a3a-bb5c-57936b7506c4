import React from 'react';
import { Button, Col, Form, Input, Row, Select } from 'antd';
import { FilterOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import type { TaskBasicSearchParams } from '../../../types/task';
import { TaskGroupSelect } from '../../common/TaskGroupSelect';

const { Option } = Select;

interface SearchFormProps {
  /** 表单实例 */
  form: any;
  /** 搜索提交回调 */
  onSearch: (values: TaskBasicSearchParams) => void;
  /** 重置回调 */
  onReset: () => void;
  /** 详细查询回调 */
  onAdvancedSearch: () => void;
  /** 样式类名 */
  className?: string;
}

/**
 * 快速搜索表单组件
 * 包含任务名称、任务分组、任务状态的快速搜索功能
 */
const SearchForm: React.FC<SearchFormProps> = ({
  form,
  onSearch,
  onReset,
  onAdvancedSearch,
  className = '',
}) => {
  return (
    <div className={`${className}`}>
      <Form form={form} onFinish={onSearch} className='w-full'>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} md={6}>
            <div className='flex flex-col gap-1 h-full justify-center'>
              <Form.Item name='name' className='mb-0'>
                <Input
                  placeholder='请输入任务名称'
                  prefix={<SearchOutlined className='text-gray-400' />}
                  allowClear
                  className='rounded-md'
                  autoComplete='off'
                  autoCorrect='off'
                  autoCapitalize='off'
                  spellCheck={false}
                />
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <div className='flex flex-col gap-1 h-full justify-center'>
              <Form.Item name='group_name' className='mb-0'>
                <TaskGroupSelect 
                  placeholder='请选择任务分组' 
                  allowClear 
                  className='w-full rounded-md' 
                  dynamicSearch={true} 
                />
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <div className='flex flex-col gap-1 h-full justify-center'>
              <Form.Item name='status' className='mb-0'>
                <Select placeholder='请选择任务状态' allowClear className='w-full rounded-md'>
                  <Option key='enabled' value='enabled'>
                    启用
                  </Option>
                  <Option key='disabled' value='disabled'>
                    禁用
                  </Option>
                </Select>
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={24} md={6}>
            <div className='flex flex-col gap-1 h-full justify-center'>
              <div className='flex gap-2 items-center h-full'>
                <Button 
                  type='primary' 
                  htmlType='submit' 
                  icon={<SearchOutlined />} 
                  className='rounded-md flex-1'
                >
                  搜索
                </Button>
                <Button 
                  onClick={onReset} 
                  className='rounded-md flex-1' 
                  icon={<ReloadOutlined />}
                >
                  重置
                </Button>
                <Button
                  icon={<FilterOutlined />}
                  onClick={onAdvancedSearch}
                  className='rounded-md flex-1'
                >
                  详细查询
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default SearchForm;
