import { useState, useEffect, useCallback } from 'react';
import type { TableRowSelection } from 'antd/es/table/interface';
import type { TaskBasic, TaskSelectionState } from '../../../types/task';

interface UseTableSelectionReturn {
  /** 当前页面选择状态 */
  selection: TaskSelectionState;
  /** 所有选中的行数据（跨页面） */
  allSelectedRows: Map<React.Key, TaskBasic>;
  /** 行选择配置 */
  rowSelection: TableRowSelection<TaskBasic>;
  /** 清空所有选择状态 */
  clearSelection: () => void;
  /** 设置所有选中的行数据 */
  setAllSelectedRows: React.Dispatch<React.SetStateAction<Map<React.Key, TaskBasic>>>;
}

/**
 * 表格选择管理Hook
 * 管理表格的选择状态，支持跨页面选择
 */
export const useTableSelection = (data: TaskBasic[]): UseTableSelectionReturn => {
  // 表格选择状态 - 支持跨页面选择
  const [selection, setSelection] = useState<TaskSelectionState>({
    selectedRowKeys: [],
    selectedRows: [],
  });

  // 存储所有已选择的行数据（跨页面）
  const [allSelectedRows, setAllSelectedRows] = useState<Map<React.Key, TaskBasic>>(new Map());

  // 更新当前页面的选择状态
  useEffect(() => {
    // 根据全局选择状态更新当前页面的选择
    const currentPageSelectedKeys = data.filter(item => allSelectedRows.has(item.id)).map(item => item.id);
    const currentPageSelectedRows = data.filter(item => allSelectedRows.has(item.id));

    setSelection({
      selectedRowKeys: currentPageSelectedKeys,
      selectedRows: currentPageSelectedRows,
    });
  }, [data, allSelectedRows]);

  // 表格行选择配置 - 支持跨页面选择
  const rowSelection: TableRowSelection<TaskBasic> = {
    selectedRowKeys: selection.selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: TaskBasic[]) => {
      // 更新当前页面的选择状态
      setSelection({
        selectedRowKeys,
        selectedRows,
      });

      // 更新全局选择状态
      const newAllSelectedRows = new Map(allSelectedRows);

      // 移除当前页面中未选中的项
      data.forEach(item => {
        if (!selectedRowKeys.includes(item.id)) {
          newAllSelectedRows.delete(item.id);
        }
      });

      // 添加当前页面中新选中的项
      selectedRows.forEach(row => {
        newAllSelectedRows.set(row.id, row);
      });

      setAllSelectedRows(newAllSelectedRows);
    },
    onSelectAll: (selected: boolean) => {
      const newAllSelectedRows = new Map(allSelectedRows);

      if (selected) {
        // 全选当前页面
        data.forEach(row => {
          newAllSelectedRows.set(row.id, row);
        });
      } else {
        // 取消选择当前页面
        data.forEach(row => {
          newAllSelectedRows.delete(row.id);
        });
      }

      setAllSelectedRows(newAllSelectedRows);

      // 更新当前页面选择状态
      const currentPageKeys = selected ? data.map(item => item.id) : [];
      const currentPageRows = selected ? data : [];
      setSelection({
        selectedRowKeys: currentPageKeys,
        selectedRows: currentPageRows,
      });
    },
  };

  // 清空所有选择状态
  const clearSelection = useCallback(() => {
    setAllSelectedRows(new Map());
    setSelection({
      selectedRowKeys: [],
      selectedRows: [],
    });
  }, []);

  return {
    selection,
    allSelectedRows,
    rowSelection,
    clearSelection,
    setAllSelectedRows,
  };
};
