import React from 'react';
import { Button, Col, Form, Input, Modal, Row, Select } from 'antd';
import { FilterOutlined } from '@ant-design/icons';
import type { TaskBasicSearchParams } from '../../../types/task';
import { FREQUENCY_OPTIONS, TASK_STATUS_OPTIONS, WEEKDAY_OPTIONS } from '../../../types/task';
import { TaskGroupSelect } from '../../common/TaskGroupSelect';

const { Option } = Select;

interface AdvancedSearchModalProps {
  /** 是否显示 */
  visible: boolean;
  /** 表单实例 */
  form: any;
  /** 初始搜索参数 */
  initialValues?: TaskBasicSearchParams;
  /** 搜索提交回调 */
  onSearch: (values: TaskBasicSearchParams) => void;
  /** 取消回调 */
  onCancel: () => void;
  /** 重置回调 */
  onReset: () => void;
}

/**
 * 高级搜索模态框组件
 * 包含所有字段的详细搜索功能
 */
const AdvancedSearchModal: React.FC<AdvancedSearchModalProps> = ({
  visible,
  form,
  initialValues,
  onSearch,
  onCancel,
  onReset,
}) => {
  return (
    <Modal
      title={
        <div className='flex items-center gap-2'>
          <FilterOutlined className='text-blue-600' />
          <span className='text-lg font-semibold'>详细查询</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      className='custom-modal'
    >
      <Form 
        form={form} 
        layout='vertical' 
        onFinish={onSearch} 
        initialValues={initialValues}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='任务名称' name='name'>
              <Input placeholder='请输入任务名称' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='任务分组' name='group'>
              <TaskGroupSelect 
                placeholder='请选择任务分组' 
                allowClear 
                dynamicSearch={true} 
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='任务状态' name='status'>
              <Select placeholder='请选择任务状态' allowClear>
                {TASK_STATUS_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='执行频率' name='frequency'>
              <Select placeholder='请选择执行频率' allowClear>
                {FREQUENCY_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='星期' name='weekday'>
              <Select placeholder='请选择星期' allowClear>
                {WEEKDAY_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='告警接收人' name='alert_receiver'>
              <Input placeholder='请输入告警接收人' />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='开始时间' name='start_time'>
              <Input placeholder='HH:mm:ss' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='结束时间' name='end_time'>
              <Input placeholder='HH:mm:ss' />
            </Form.Item>
          </Col>
        </Row>
        <div className='flex justify-end gap-3 pt-4 border-t border-gray-200'>
          <Button
            onClick={onReset}
            className='rounded-md px-6 py-2'
          >
            重置
          </Button>
          <Button 
            type='primary' 
            htmlType='submit' 
            className='rounded-md px-6 py-2'
          >
            查询
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default AdvancedSearchModal;
