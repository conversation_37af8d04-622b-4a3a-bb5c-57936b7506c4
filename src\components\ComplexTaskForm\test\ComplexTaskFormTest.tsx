import React, { useState } from 'react';
import { App, Drawer, But<PERSON>, Space } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { ComplexTaskFormRefactored } from '../index';
import ComplexTaskForm from '../../ComplexTaskForm'; // 原始组件

/**
 * ComplexTaskForm 重构测试页面
 */
const ComplexTaskFormTest: React.FC = () => {
  const [refactoredDrawer, setRefactoredDrawer] = useState(false);
  const [originalDrawer, setOriginalDrawer] = useState(false);

  return (
    <App>
      <div style={{ height: '100vh', padding: '20px' }}>
        <h1 style={{ marginBottom: '20px', textAlign: 'center' }}>
          ComplexTaskForm 组件重构测试
        </h1>
        
        <div style={{ marginBottom: '20px', padding: '16px', backgroundColor: '#f0f9ff', borderRadius: '6px', border: '1px solid #0ea5e9' }}>
          <h3 style={{ color: '#0369a1', marginBottom: '12px' }}>🎉 ComplexTaskForm 重构完成！</h3>
          <div style={{ color: '#0c4a6e' }}>
            <p><strong>✅ 功能保持：</strong>所有原有功能完全保留，包括基本信息、告警配置、数据库连接、其他信息等</p>
            <p><strong>✅ 结构优化：</strong>将 1271 行的单一组件拆分为多个小组件和 hooks</p>
            <p><strong>✅ 模块化设计：</strong>4个标签页组件 + 4个自定义hooks + 工具函数</p>
            <p><strong>✅ 可维护性：</strong>代码结构清晰，职责分离，易于维护和扩展</p>
          </div>
        </div>

        <div style={{ textAlign: 'center' }}>
          <Space size="large">
            <Button 
              type="primary" 
              size="large" 
              icon={<EditOutlined />}
              onClick={() => setRefactoredDrawer(true)}
            >
              测试重构后的组件
            </Button>
            <Button 
              size="large" 
              icon={<EditOutlined />}
              onClick={() => setOriginalDrawer(true)}
            >
              测试原始组件
            </Button>
          </Space>
        </div>

        <div style={{ marginTop: '40px', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
          <h3>重构详情：</h3>
          <ul>
            <li><strong>BasicInfoForm</strong> - 基本信息表单组件</li>
            <li><strong>AlertConfigTab</strong> - 告警配置标签页组件</li>
            <li><strong>DatabaseConfigTab</strong> - 数据库连接配置组件</li>
            <li><strong>OtherInfoTab</strong> - 其他信息配置组件</li>
            <li><strong>useFormData</strong> - 表单数据管理 Hook</li>
            <li><strong>useModalStates</strong> - Modal状态管理 Hook</li>
            <li><strong>useDataLoading</strong> - 数据加载管理 Hook</li>
            <li><strong>useFormSubmit</strong> - 表单提交管理 Hook</li>
          </ul>
        </div>

        {/* 重构后的组件抽屉 */}
        <Drawer
          title={
            <div className='flex items-center gap-2'>
              <EditOutlined className='text-blue-600' />
              <span className='text-lg font-semibold'>重构后的 ComplexTaskForm</span>
            </div>
          }
          width='90%'
          open={refactoredDrawer}
          onClose={() => setRefactoredDrawer(false)}
          maskClosable={false}
          className='custom-drawer'
          footer={null}
        >
          <ComplexTaskFormRefactored
            onSubmit={() => {
              setRefactoredDrawer(false);
              console.log('重构组件提交成功');
            }}
            onCancel={() => setRefactoredDrawer(false)}
            onReset={() => {
              console.log('重构组件重置');
            }}
          />
        </Drawer>

        {/* 原始组件抽屉 */}
        <Drawer
          title={
            <div className='flex items-center gap-2'>
              <EditOutlined className='text-gray-600' />
              <span className='text-lg font-semibold'>原始的 ComplexTaskForm</span>
            </div>
          }
          width='90%'
          open={originalDrawer}
          onClose={() => setOriginalDrawer(false)}
          maskClosable={false}
          className='custom-drawer'
          footer={null}
        >
          <ComplexTaskForm
            onSubmit={() => {
              setOriginalDrawer(false);
              console.log('原始组件提交成功');
            }}
            onCancel={() => setOriginalDrawer(false)}
            onReset={() => {
              console.log('原始组件重置');
            }}
          />
        </Drawer>
      </div>
    </App>
  );
};

export default ComplexTaskFormTest;
