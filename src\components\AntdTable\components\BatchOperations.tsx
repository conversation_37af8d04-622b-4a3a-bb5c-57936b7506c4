import React from 'react';
import { Button } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import type { TaskBasic } from '../../../types/task';

interface BatchOperationsProps {
  /** 所有选中的行数据 */
  allSelectedRows: Map<React.Key, TaskBasic>;
  /** 批量删除回调 */
  onBatchDelete: () => void;
  /** 取消全选回调 */
  onCancelSelection: () => void;
  /** 样式对象 */
  styles: any;
}

/**
 * 批量操作组件
 * 当有选中项时显示，提供批量删除和取消全选功能
 */
const BatchOperations: React.FC<BatchOperationsProps> = ({
  allSelectedRows,
  onBatchDelete,
  onCancelSelection,
  styles,
}) => {
  if (allSelectedRows.size === 0) {
    return null;
  }

  return (
    <div className={styles.batchOperations}>
      <div className='flex items-center gap-4 h-full'>
        <div className='flex items-center gap-2'>
          <div className={styles.pulseDot}></div>
          <span className='text-blue-700 font-medium'>
            已选择 {allSelectedRows.size} 项（跨页面选择）
          </span>
        </div>
        <div className='flex items-center gap-2'>
          <Button 
            type='text' 
            danger 
            onClick={onBatchDelete} 
            className={styles.batchDeleteBtn} 
            icon={<DeleteOutlined />}
          >
            批量删除
          </Button>
          <Button
            type='text'
            onClick={onCancelSelection}
            className={styles.batchCancelBtn}
          >
            取消全选
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BatchOperations;
