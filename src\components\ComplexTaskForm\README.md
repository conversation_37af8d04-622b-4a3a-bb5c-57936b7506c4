# ComplexTaskForm 组件重构

本目录包含了重构后的 ComplexTaskForm 组件，将原来的 1271 行单一大文件拆分为多个小的、可复用的组件和 hooks。

## 目录结构

```
src/components/ComplexTaskForm/
├── components/           # 子组件
│   ├── BasicInfoForm.tsx        # 基本信息表单组件
│   ├── AlertConfigTab.tsx       # 告警配置标签页组件
│   ├── DatabaseConfigTab.tsx    # 数据库连接配置组件
│   ├── OtherInfoTab.tsx         # 其他信息配置组件
│   └── index.ts                 # 组件导出文件
├── hooks/                # 自定义 Hooks
│   ├── useFormData.ts           # 表单数据管理 Hook
│   ├── useModalStates.ts        # Modal状态管理 Hook
│   ├── useDataLoading.ts        # 数据加载管理 Hook
│   ├── useFormSubmit.ts         # 表单提交管理 Hook
│   └── index.ts                 # Hooks 导出文件
├── test/                 # 测试文件
│   └── ComplexTaskFormTest.tsx  # 组件测试页面
├── ComplexTaskFormRefactored.tsx # 重构后的主组件
├── index.ts                     # 主导出文件
└── README.md                    # 说明文档
```

## 组件说明

### 主组件
- **ComplexTaskFormRefactored.tsx**: 重构后的主组件，使用子组件和 hooks 组合而成

### 子组件
- **BasicInfoForm**: 基本信息表单，包含任务名称、分组、时间、状态等基本配置
- **AlertConfigTab**: 告警配置标签页，包含告警规则和告警发送的配置
- **DatabaseConfigTab**: 数据库连接配置，包含数据库连接的配置和管理
- **OtherInfoTab**: 其他信息配置，包含其他信息的配置和管理

### 自定义 Hooks
- **useFormData**: 管理表单的各种数据状态和初始化逻辑
- **useModalStates**: 管理各种Modal的显示状态
- **useDataLoading**: 管理可选择数据的加载
- **useFormSubmit**: 管理表单的提交逻辑和数据处理

## 使用方式

### 基本使用
```tsx
import { ComplexTaskFormRefactored } from './components/ComplexTaskForm';

const MyPage = () => {
  return (
    <ComplexTaskFormRefactored
      onSubmit={() => console.log('提交成功')}
      onCancel={() => console.log('取消')}
      onReset={() => console.log('重置')}
    />
  );
};
```

### 使用子组件
```tsx
import { BasicInfoForm, AlertConfigTab } from './components/ComplexTaskForm';

const CustomForm = () => {
  const [form] = Form.useForm();
  
  return (
    <div>
      <BasicInfoForm form={form} />
      <AlertConfigTab 
        alerts={alerts}
        alertSends={alertSends}
        onAddAlert={handleAddAlert}
        // ... 其他props
      />
    </div>
  );
};
```

### 使用 Hooks
```tsx
import { useFormData, useModalStates } from './components/ComplexTaskForm';

const CustomComponent = () => {
  const { alerts, setAlerts, initializeFormData } = useFormData(initialData);
  const { alertModal, setAlertModal } = useModalStates();
  
  // 自定义逻辑
};
```

## 重构优势

1. **模块化设计**: 将大组件拆分为职责单一的小组件
2. **可复用性**: 子组件和 hooks 可以在其他地方复用
3. **可维护性**: 代码结构清晰，易于维护和扩展
4. **可测试性**: 每个组件和 hook 都可以独立测试
5. **类型安全**: 完整的 TypeScript 类型定义
6. **性能优化**: 通过 hooks 优化状态管理和渲染性能

## 功能保持

重构后的组件保持了原组件的所有功能：
- ✅ 基本信息表单配置
- ✅ 告警规则和告警发送配置
- ✅ 数据库连接配置
- ✅ 其他信息配置
- ✅ 标签页切换
- ✅ Modal弹窗管理
- ✅ 表单验证和提交
- ✅ 数据加载和处理
- ✅ 所有样式和交互效果

## 标签页功能

### 基本信息
- 任务名称、分组选择
- 开始时间、结束时间、状态
- 星期选择、重试次数
- 执行频率、重试间隔

### 告警配置
- 告警规则管理（新增、编辑、删除、选择已有）
- 告警发送管理（新增、编辑、删除、选择已有）
- 表格展示和操作

### 数据库连接
- 数据库连接配置（新增、编辑、删除、选择已有）
- 连接信息展示
- 单一连接管理

### 其他信息
- 其他信息配置（新增、编辑、删除、选择已有）
- 信息详情展示
- 单一信息管理

## 测试

运行测试组件：
```tsx
import ComplexTaskFormTest from './components/ComplexTaskForm/test/ComplexTaskFormTest';

// 在你的路由或页面中使用
<ComplexTaskFormTest />
```

## 技术实现

### 状态管理优化
- 使用自定义 Hooks 管理不同类型的状态
- 避免状态冗余和不必要的更新
- 提高组件性能

### 组件通信
- 通过 props 传递数据和回调函数
- 使用 hooks 共享状态和逻辑
- 保持数据流的清晰性

### 代码组织
- 按功能模块组织代码
- 统一的导入导出规范
- 清晰的文件命名约定
