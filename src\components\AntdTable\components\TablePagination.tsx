import React from 'react';
import { Pagination } from 'antd';

interface TablePaginationProps {
  /** 当前页码 */
  current: number;
  /** 每页条数 */
  pageSize: number;
  /** 总条数 */
  total: number;
  /** 分页变化回调 */
  onChange: (page: number, pageSize: number) => void;
  /** 样式对象 */
  styles: any;
}

/**
 * 表格分页组件
 * 封装了分页的基本功能和样式
 */
const TablePagination: React.FC<TablePaginationProps> = ({
  current,
  pageSize,
  total,
  onChange,
  styles,
}) => {
  return (
    <div className='flex-shrink-0 h-12 border-t border-gray-200 bg-gray-50 flex items-center'>
      <div className='px-4 w-full h-full flex items-center'>
        <div className='flex justify-between items-center w-full h-full'>
          <div className={styles.dataStats}>
            <span>共 {total} 条数据</span>
          </div>
          <Pagination
            current={current}
            pageSize={pageSize}
            total={total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`}
            className='custom-pagination'
            pageSizeOptions={['15', '20', '30', '50', '100', '500']}
            locale={{
              items_per_page: '条/页',
              jump_to: '跳至',
              jump_to_confirm: '确定',
              page: '页',
              prev_page: '上一页',
              next_page: '下一页',
              prev_5: '向前 5 页',
              next_5: '向后 5 页',
              prev_3: '向前 3 页',
              next_3: '向后 3 页',
            }}
            onChange={onChange}
          />
        </div>
      </div>
    </div>
  );
};

export default TablePagination;
