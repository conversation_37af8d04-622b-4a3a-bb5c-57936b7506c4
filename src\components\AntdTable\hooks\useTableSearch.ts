import { useState, useCallback } from 'react';
import type { TableProps } from 'antd';
import type { FilterValue, SorterResult } from 'antd/es/table/interface';
import type { TaskBasic, TaskBasicSearchParams, TaskModalState } from '../../../types/task';

interface UseTableSearchReturn {
  /** 搜索参数状态 */
  searchParams: TaskBasicSearchParams;
  /** 设置搜索参数 */
  setSearchParams: React.Dispatch<React.SetStateAction<TaskBasicSearchParams>>;
  /** 排序状态 */
  sortedInfo: SorterResult<TaskBasic> | SorterResult<TaskBasic>[];
  /** 设置排序状态 */
  setSortedInfo: React.Dispatch<React.SetStateAction<SorterResult<TaskBasic> | SorterResult<TaskBasic>[]>>;
  /** 筛选状态 */
  filteredInfo: Record<string, FilterValue | null>;
  /** 设置筛选状态 */
  setFilteredInfo: React.Dispatch<React.SetStateAction<Record<string, FilterValue | null>>>;
  /** 搜索Modal状态 */
  searchModal: TaskModalState;
  /** 设置搜索Modal状态 */
  setSearchModal: React.Dispatch<React.SetStateAction<TaskModalState>>;
  /** 表格变化处理函数 */
  handleTableChange: TableProps<TaskBasic>['onChange'];
  /** 重置搜索状态 */
  resetSearchState: () => void;
}

/**
 * 表格搜索管理Hook
 * 管理表格的搜索、排序、筛选等状态
 */
export const useTableSearch = (): UseTableSearchReturn => {
  // 搜索参数状态
  const [searchParams, setSearchParams] = useState<TaskBasicSearchParams>({});

  // 排序状态
  const [sortedInfo, setSortedInfo] = useState<SorterResult<TaskBasic> | SorterResult<TaskBasic>[]>({});

  // 筛选状态
  const [filteredInfo, setFilteredInfo] = useState<Record<string, FilterValue | null>>({});

  // Modal状态
  const [searchModal, setSearchModal] = useState<TaskModalState>({
    visible: false,
  });

  // 表格变化处理函数
  const handleTableChange: TableProps<TaskBasic>['onChange'] = useCallback((pagination, filters, sorter) => {
    console.log('表格变化:', { pagination, filters, sorter });
    setFilteredInfo(filters || {});
    setSortedInfo(sorter || {});
  }, []);

  // 重置搜索状态
  const resetSearchState = useCallback(() => {
    setSearchParams({});
    setSortedInfo({});
    setFilteredInfo({});
    setSearchModal({ visible: false });
  }, []);

  return {
    searchParams,
    setSearchParams,
    sortedInfo,
    setSortedInfo,
    filteredInfo,
    setFilteredInfo,
    searchModal,
    setSearchModal,
    handleTableChange,
    resetSearchState,
  };
};
