# AntdTable 组件重构

本目录包含了重构后的 AntdTable 组件，将原来的单一大文件拆分为多个小的、可复用的组件和 hooks。

## 目录结构

```
src/components/AntdTable/
├── components/           # 子组件
│   ├── SearchForm.tsx           # 快速搜索表单组件
│   ├── AdvancedSearchModal.tsx  # 高级搜索模态框组件
│   ├── TaskTable.tsx            # 任务表格组件
│   ├── BatchOperations.tsx      # 批量操作组件
│   ├── TablePagination.tsx      # 表格分页组件
│   └── index.ts                 # 组件导出文件
├── hooks/                # 自定义 Hooks
│   ├── useTableData.ts          # 表格数据管理 Hook
│   ├── useTableSelection.ts     # 表格选择管理 Hook
│   ├── useTableSearch.ts        # 表格搜索管理 Hook
│   ├── useTableActions.ts       # 表格操作管理 Hook
│   └── index.ts                 # Hooks 导出文件
├── utils/                # 工具函数
│   ├── tableColumns.tsx         # 表格列配置工具
│   └── index.ts                 # 工具函数导出文件
├── test/                 # 测试文件
│   └── AntdTableTest.tsx        # 组件测试页面
├── AntdTableRefactored.tsx      # 重构后的主组件
├── index.ts                     # 主导出文件
└── README.md                    # 说明文档
```

## 组件说明

### 主组件
- **AntdTableRefactored.tsx**: 重构后的主组件，使用子组件和 hooks 组合而成

### 子组件
- **SearchForm**: 快速搜索表单，包含任务名称、分组、状态的搜索
- **AdvancedSearchModal**: 高级搜索模态框，包含所有字段的详细搜索
- **TaskTable**: 任务表格，封装了表格的基本功能
- **BatchOperations**: 批量操作栏，显示选中项数量和批量操作按钮
- **TablePagination**: 表格分页，封装了分页的基本功能

### 自定义 Hooks
- **useTableData**: 管理表格数据的加载、分页等状态
- **useTableSelection**: 管理表格的选择状态，支持跨页面选择
- **useTableSearch**: 管理表格的搜索、排序、筛选等状态
- **useTableActions**: 管理表格的删除等操作

### 工具函数
- **tableColumns**: 表格列配置相关的工具函数
- **index**: 通用工具函数，如高度计算、数据格式化等

## 使用方式

### 基本使用
```tsx
import { AntdTableRefactored } from './components/AntdTable';

const MyPage = () => {
  return (
    <div style={{ height: '100vh' }}>
      <AntdTableRefactored contentHeight={800} />
    </div>
  );
};
```

### 使用子组件
```tsx
import { SearchForm, TaskTable } from './components/AntdTable';

const CustomTable = () => {
  // 自定义逻辑
  return (
    <div>
      <SearchForm onSearch={handleSearch} />
      <TaskTable data={data} loading={loading} />
    </div>
  );
};
```

### 使用 Hooks
```tsx
import { useTableData, useTableSelection } from './components/AntdTable';

const CustomComponent = () => {
  const { data, loading, loadData } = useTableData(searchParams);
  const { selection, rowSelection } = useTableSelection(data);
  
  // 自定义逻辑
};
```

## 重构优势

1. **模块化**: 将大组件拆分为小的、职责单一的组件
2. **可复用**: 子组件和 hooks 可以在其他地方复用
3. **可维护**: 代码结构清晰，易于维护和扩展
4. **可测试**: 每个组件和 hook 都可以独立测试
5. **类型安全**: 完整的 TypeScript 类型定义
6. **性能优化**: 通过 hooks 优化状态管理和渲染性能

## 功能保持

重构后的组件保持了原组件的所有功能：
- ✅ 快速搜索和高级搜索
- ✅ 表格排序、筛选、分页
- ✅ 跨页面选择和批量操作
- ✅ 新增、编辑、删除任务
- ✅ 分组管理
- ✅ 响应式布局
- ✅ 所有样式和交互效果

## 测试

运行测试组件：
```tsx
import AntdTableTest from './components/AntdTable/test/AntdTableTest';

// 在你的路由或页面中使用
<AntdTableTest />
```
