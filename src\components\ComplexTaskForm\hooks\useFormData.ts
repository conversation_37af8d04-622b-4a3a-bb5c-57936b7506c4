import { useState, useEffect } from 'react';
import { message } from 'antd';
import { TaskService } from '../../../services/taskService';
import { parseFrequencyFromString } from '../../../utils/frequencyConverter';
import type { TaskBasic, TaskAlert, DBConnection, AlertSend, OtherInfo } from '../../../types/task';

interface UseFormDataReturn {
  /** 是否为编辑模式 */
  isEditMode: boolean;
  /** 告警规则数据 */
  alerts: TaskAlert[];
  /** 设置告警规则数据 */
  setAlerts: React.Dispatch<React.SetStateAction<TaskAlert[]>>;
  /** 告警发送数据 */
  alertSends: AlertSend[];
  /** 设置告警发送数据 */
  setAlertSends: React.Dispatch<React.SetStateAction<AlertSend[]>>;
  /** 数据库连接数据 */
  dbConnection: DBConnection | null;
  /** 设置数据库连接数据 */
  setDbConnection: React.Dispatch<React.SetStateAction<DBConnection | null>>;
  /** 其他信息数据 */
  otherInfo: OtherInfo | null;
  /** 设置其他信息数据 */
  setOtherInfo: React.Dispatch<React.SetStateAction<OtherInfo | null>>;
  /** 初始化表单数据 */
  initializeFormData: (form: any) => Promise<void>;
}

/**
 * 表单数据管理Hook
 * 管理表单的各种数据状态和初始化逻辑
 */
export const useFormData = (initialData?: TaskBasic): UseFormDataReturn => {
  // 是否为编辑模式
  const [isEditMode, setEditMode] = useState(false);

  // 各种数据状态
  const [alerts, setAlerts] = useState<TaskAlert[]>([]);
  const [alertSends, setAlertSends] = useState<AlertSend[]>([]);
  const [dbConnection, setDbConnection] = useState<DBConnection | null>(null);
  const [otherInfo, setOtherInfo] = useState<OtherInfo | null>(null);

  // 初始化表单数据
  const initializeFormData = async (form: any) => {
    if (initialData) {
      // 编辑模式
      setEditMode(true);

      // 处理执行频率和重试频率
      const taskExec = {
        ...initialData,
      };

      // 处理执行频率 - 解析格式如 "40sec"、"5hour" 等
      if (taskExec.frequency && typeof taskExec.frequency === 'string') {
        const parsedFrequency = parseFrequencyFromString(taskExec.frequency, false);
        if (parsedFrequency) {
          taskExec.frequency = parsedFrequency;
        }
      }

      // 处理重试频率 - 解析格式如 "40sec"、"5hour" 等
      if (taskExec.retry_frequency && typeof taskExec.retry_frequency === 'string') {
        const parsedRetryFrequency = parseFrequencyFromString(taskExec.retry_frequency, true);
        if (parsedRetryFrequency) {
          taskExec.retry_frequency = parsedRetryFrequency;
        }
      }

      form.setFieldsValue(taskExec);

      // 加载关联数据
      try {
        // 加载告警数据
        if (initialData.alert_task_id && initialData.alert_task_id.length > 0) {
          try {
            const alertsData = await TaskService.getAlertsByIds(initialData.alert_task_id);
            setAlerts(alertsData);
          } catch (error) {
            console.error('获取告警数据失败:', error);
          }
        }

        // 加载告警发送数据
        if (initialData.alert_send_id && initialData.alert_send_id.length > 0) {
          try {
            const alertSendsData = await TaskService.getAlertSendsByIds(initialData.alert_send_id);
            setAlertSends(alertSendsData);
          } catch (error) {
            console.error('获取告警发送数据失败:', error);
          }
        }

        // 加载数据库连接数据
        if (initialData.db_connection_id) {
          try {
            const dbConnectionData = await TaskService.getDbConnectionById(initialData.db_connection_id);
            setDbConnection(dbConnectionData);
          } catch (error) {
            console.error('获取数据库连接数据失败:', error);
          }
        }

        // 加载其他信息数据
        if (initialData.other_info_id) {
          try {
            const otherInfoData = await TaskService.getOtherInfoById(initialData.other_info_id);
            setOtherInfo(otherInfoData);
          } catch (error) {
            console.error('获取其他信息数据失败:', error);
          }
        }
      } catch (error) {
        console.error('加载关联数据失败:', error);
      }
    } else {
      // 新增模式
      setEditMode(false);

      // 新增模式时清空表单
      form.resetFields();
      setAlerts([]);
      setAlertSends([]);
      setDbConnection(null);
      setOtherInfo(null);
    }
  };

  return {
    isEditMode,
    alerts,
    setAlerts,
    alertSends,
    setAlertSends,
    dbConnection,
    setDbConnection,
    otherInfo,
    setOtherInfo,
    initializeFormData,
  };
};
