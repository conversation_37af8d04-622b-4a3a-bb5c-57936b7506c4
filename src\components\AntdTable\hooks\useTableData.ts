import { useState, useCallback } from 'react';
import { App } from 'antd';
import { TaskService } from '../../../services/taskService';
import type { TaskBasic, TaskBasicSearchParams } from '../../../types/task';

interface UseTableDataReturn {
  /** 表格数据 */
  data: TaskBasic[];
  /** 加载状态 */
  loading: boolean;
  /** 总条数 */
  total: number;
  /** 分页状态 */
  pagination: {
    current: number;
    page_size: number;
  };
  /** 设置分页状态 */
  setPagination: React.Dispatch<React.SetStateAction<{
    current: number;
    page_size: number;
  }>>;
  /** 加载数据 */
  loadData: (customParams?: Partial<TaskBasicSearchParams>) => Promise<void>;
}

/**
 * 表格数据管理Hook
 * 管理表格数据的加载、分页等状态
 */
export const useTableData = (searchParams: TaskBasicSearchParams): UseTableDataReturn => {
  const { message } = App.useApp();
  
  // 表格数据状态
  const [data, setData] = useState<TaskBasic[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    page_size: 15,
  });

  // 加载数据
  const loadData = useCallback(
    async (customParams?: Partial<TaskBasicSearchParams>) => {
      setLoading(true);

      try {
        // 使用传入的自定义参数，如果没有则使用当前状态
        const currentPage = customParams?.current ?? pagination.current;
        const currentPageSize = customParams?.page_size ?? pagination.page_size;

        // 更新搜索参数状态
        const params = {
          // searchParams (组件状态)
          // 持久化：保存在组件状态中，跨多次调用保持
          // 用途：存储用户的搜索条件，在分页、删除等操作时继续使用
          // 生命周期：直到用户重置或修改搜索条件
          ...searchParams,
          // 分页参数（次高优先级）
          current: currentPage,
          page_size: currentPageSize,
          // customParams (函数参数) （最高优先级）
          // 临时性：仅在当次调用中生效
          // 用途：临时覆盖某些参数，如强制重置到第一页
          // 优先级：最高，可以覆盖 searchParams 中的同名字段
          ...customParams,
        };

        const response = await TaskService.getTasks(params);

        // 检查返回数据是否为空
        if (!response || !response.data || response.data == null) {
          console.warn('获取任务数据失败：返回数据为空');
          message.warning('未获取到任务数据');
          setData([]);
          setTotal(0);
          return;
        }

        // 检查数据数组是否为空
        if (!Array.isArray(response.data)) {
          console.warn('获取任务数据失败：数据格式不正确');
          message.warning('数据格式错误');
          setData([]);
          setTotal(0);
          return;
        }

        setData(response.data);
        setTotal(response.total || 0);
      } catch (error) {
        message.error('加载数据失败');
        console.error('加载数据失败:', error);
      } finally {
        setLoading(false);
      }
    },
    [pagination, message, searchParams]
  );

  return {
    data,
    loading,
    total,
    pagination,
    setPagination,
    loadData,
  };
};
