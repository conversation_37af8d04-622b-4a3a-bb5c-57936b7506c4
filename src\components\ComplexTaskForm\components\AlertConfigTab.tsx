import React from 'react';
import { Card, Row, Col, Space, Table, Button } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import type { TaskAlert, AlertSend } from '../../../types/task';

interface AlertConfigTabProps {
  /** 告警规则数据 */
  alerts: TaskAlert[];
  /** 告警发送数据 */
  alertSends: AlertSend[];
  /** 新增告警规则 */
  onAddAlert: () => void;
  /** 编辑告警规则 */
  onEditAlert: (index: number) => void;
  /** 删除告警规则 */
  onDeleteAlert: (index: number) => void;
  /** 选择已有告警规则 */
  onSelectAlert: () => void;
  /** 新增告警发送 */
  onAddAlertSend: () => void;
  /** 编辑告警发送 */
  onEditAlertSend: (index: number) => void;
  /** 删除告警发送 */
  onDeleteAlertSend: (index: number) => void;
  /** 选择已有告警发送 */
  onSelectAlertSend: () => void;
}

/**
 * 告警配置标签页组件
 * 包含告警规则和告警发送的配置
 */
const AlertConfigTab: React.FC<AlertConfigTabProps> = ({
  alerts,
  alertSends,
  onAddAlert,
  onEditAlert,
  onDeleteAlert,
  onSelectAlert,
  onAddAlertSend,
  onEditAlertSend,
  onDeleteAlertSend,
  onSelectAlertSend,
}) => {
  // 告警规则表格列定义
  const alertColumns = [
    {
      title: '告警名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '告警类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
    },
    {
      title: '触发条件',
      dataIndex: 'condition',
      key: 'condition',
      width: 200,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: TaskAlert, index: number) => (
        <Space size='small'>
          <Button
            type='text'
            size='small'
            icon={<EyeOutlined />}
            onClick={() => onEditAlert(index)}
          >
            查看
          </Button>
          <Button
            type='text'
            size='small'
            icon={<EditOutlined />}
            onClick={() => onEditAlert(index)}
          >
            编辑
          </Button>
          <Button
            type='text'
            size='small'
            danger
            icon={<DeleteOutlined />}
            onClick={() => onDeleteAlert(index)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 告警发送表格列定义
  const alertSendColumns = [
    {
      title: '发送方式',
      dataIndex: 'type',
      key: 'type',
      width: 120,
    },
    {
      title: '接收人',
      dataIndex: 'receiver',
      key: 'receiver',
      width: 150,
    },
    {
      title: '配置信息',
      dataIndex: 'config',
      key: 'config',
      width: 200,
      ellipsis: true,
      render: (config: any) => {
        if (typeof config === 'object') {
          return JSON.stringify(config);
        }
        return config;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: AlertSend, index: number) => (
        <Space size='small'>
          <Button
            type='text'
            size='small'
            icon={<EyeOutlined />}
            onClick={() => onEditAlertSend(index)}
          >
            查看
          </Button>
          <Button
            type='text'
            size='small'
            icon={<EditOutlined />}
            onClick={() => onEditAlertSend(index)}
          >
            编辑
          </Button>
          <Button
            type='text'
            size='small'
            danger
            icon={<DeleteOutlined />}
            onClick={() => onDeleteAlertSend(index)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 告警规则配置 */}
      <Card title='告警规则配置' className='mb-4'>
        <Row gutter={16} className='mb-4'>
          <Col span={24}>
            <Space>
              <Button type='primary' icon={<PlusOutlined />} onClick={onAddAlert}>
                新增告警规则
              </Button>
              <Button icon={<PlusOutlined />} onClick={onSelectAlert}>
                选择已有告警规则
              </Button>
            </Space>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Table
              columns={alertColumns}
              dataSource={alerts}
              rowKey={(record, index) => `alert-${index}`}
              pagination={false}
              size='small'
              locale={{
                emptyText: '暂无告警规则',
              }}
            />
          </Col>
        </Row>
      </Card>

      {/* 告警发送配置 */}
      <Card title='告警发送配置' className='mb-4'>
        <Row gutter={16} className='mb-4'>
          <Col span={24}>
            <Space>
              <Button type='primary' icon={<PlusOutlined />} onClick={onAddAlertSend}>
                新增告警发送
              </Button>
              <Button icon={<PlusOutlined />} onClick={onSelectAlertSend}>
                选择已有告警发送
              </Button>
            </Space>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Table
              columns={alertSendColumns}
              dataSource={alertSends}
              rowKey={(record, index) => `alertSend-${index}`}
              pagination={false}
              size='small'
              locale={{
                emptyText: '暂无告警发送配置',
              }}
            />
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default AlertConfigTab;
