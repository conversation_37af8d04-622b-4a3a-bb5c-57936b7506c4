import { useState } from 'react';
import { message } from 'antd';
import { TaskService } from '../../../services/taskService';
import { formatFrequencyToString } from '../../../utils/frequencyConverter';
import type { 
  TaskBasic, 
  TaskAlert, 
  DBConnection, 
  AlertSend, 
  OtherInfo,
  TaskBasicFormSubmitData,
  TaskBasicFormDataAdd,
  TaskBasicFormDataUpdateOrDelete
} from '../../../types/task';

interface UseFormSubmitProps {
  /** 是否为编辑模式 */
  isEditMode: boolean;
  /** 初始数据 */
  initialData?: TaskBasic;
  /** 告警规则数据 */
  alerts: TaskAlert[];
  /** 告警发送数据 */
  alertSends: AlertSend[];
  /** 数据库连接数据 */
  dbConnection: DBConnection | null;
  /** 其他信息数据 */
  otherInfo: OtherInfo | null;
  /** 提交成功回调 */
  onSubmit?: () => void;
}

interface UseFormSubmitReturn {
  /** 提交加载状态 */
  submitLoading: boolean;
  /** 处理表单提交 */
  handleSubmit: (values: TaskBasicFormSubmitData) => Promise<void>;
}

/**
 * 表单提交管理Hook
 * 管理表单的提交逻辑和数据处理
 */
export const useFormSubmit = ({
  isEditMode,
  initialData,
  alerts,
  alertSends,
  dbConnection,
  otherInfo,
  onSubmit,
}: UseFormSubmitProps): UseFormSubmitReturn => {
  const [submitLoading, setSubmitLoading] = useState(false);

  // 提交表单
  const handleSubmit = async (values: TaskBasicFormSubmitData) => {
    try {
      setSubmitLoading(true);
      console.log('表单提交数据:', values);

      // 处理表单数据，转换回原始格式
      // 处理执行频率 - 转换为 "40sec"、"5hour" 等格式
      const processedFrequency = values.frequency && typeof values.frequency === 'object' 
        ? formatFrequencyToString(values.frequency, false) 
        : values.frequency;

      // 处理重试频率 - 转换为 "40sec"、"5hour" 等格式
      const processedRetryFrequency = values.retry_frequency && typeof values.retry_frequency === 'object' 
        ? formatFrequencyToString(values.retry_frequency, true) 
        : values.retry_frequency;

      // 根据是否有ID区分新增和更新
      if (isEditMode && initialData?.id) {
        // 更新操作 - 使用TaskBasicFormDataUpdateOrDelete类型
        const updateData: TaskBasicFormDataUpdateOrDelete = {
          id: initialData.id,
          name: values.name,
          group_name: values.group_name,
          status: values.status,
          start_time: values.start_time,
          end_time: values.end_time,
          weekday: Array.isArray(values.weekday) ? values.weekday.join(',') : values.weekday,
          frequency: processedFrequency,
          retry_num: values.retry_num?.toString() || '0',
          retry_frequency: processedRetryFrequency,
          alert_task_id: alerts.map(alert => alert.id?.toString() || '').join(','),
          alert_send_id: alertSends.map(send => send.id?.toString() || '').join(','),
          db_connection_id: dbConnection?.id?.toString() || '',
          other_info_id: otherInfo?.id?.toString() || '',
        };

        console.log('更新任务数据:', updateData);
        const response = await TaskService.updateComplexForm(updateData);
        console.log('更新任务响应:', response);
        message.success('任务更新成功');
      } else {
        // 新增操作 - 使用TaskBasicFormDataAdd类型
        const addData: TaskBasicFormDataAdd = {
          name: values.name,
          group_name: values.group_name,
          status: values.status,
          start_time: values.start_time,
          end_time: values.end_time,
          weekday: Array.isArray(values.weekday) ? values.weekday.join(',') : values.weekday,
          frequency: processedFrequency,
          retry_num: values.retry_num?.toString() || '0',
          retry_frequency: processedRetryFrequency,
          alert_task_id: alerts.map(alert => alert.id?.toString() || '').join(','),
          alert_send_id: alertSends.map(send => send.id?.toString() || '').join(','),
          db_connection_id: dbConnection?.id?.toString() || '',
          other_info_id: otherInfo?.id?.toString() || '',
        };

        console.log('新增任务数据:', addData);
        const response = await TaskService.saveComplexForm(addData);
        console.log('新增任务响应:', response);
        message.success('任务创建成功');
      }

      // 调用成功回调
      if (onSubmit) {
        onSubmit();
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败: ' + error);
    } finally {
      setSubmitLoading(false);
    }
  };

  return {
    submitLoading,
    handleSubmit,
  };
};
