import { createBrowserRouter } from 'react-router-dom';
import { DefaultLayout } from './layouts/DefaultLayout';
import DashboardPage from './pages/DashboardPage';
import UserManagementPage from './pages/UserManagementPage';
import TeamCollaborationPage from './pages/TeamCollaborationPage';
import TaskTablePage from './pages/TaskTablePage';

export const router = createBrowserRouter([
  {
    path: '/',
    Component: DefaultLayout,
    children: [
      {
        index: true,
        Component: () => <DashboardPage />,
      },
      {
        path: 'dashboard',
        Component: () => <DashboardPage />,
      },
      {
        path: 'users',
        Component: () => <UserManagementPage />,
      },
      {
        path: 'tasks',
        Component: () => <TaskTablePage />,
      },
      {
        path: 'teams',
        Component: () => <TeamCollaborationPage />,
      },
    ],
  },
  {
    path: '*',
    Component: () => <div>404 Not Found</div>,
  },
]);
