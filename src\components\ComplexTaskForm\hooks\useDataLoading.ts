import { useState, useEffect } from 'react';
import { message } from 'antd';
import { TaskService } from '../../../services/taskService';
import type { TaskBasic, TaskAlert, DBConnection, AlertSend, OtherInfo } from '../../../types/task';

interface UseDataLoadingReturn {
  /** 可选择的告警规则 */
  availableAlerts: TaskAlert[];
  /** 可选择的数据库连接 */
  availableDbConnections: DBConnection[];
  /** 可选择的告警发送 */
  availableAlertSends: AlertSend[];
  /** 可选择的其他信息 */
  availableOtherInfos: OtherInfo[];
  /** 重新加载数据 */
  reloadData: () => Promise<void>;
}

/**
 * 数据加载管理Hook
 * 管理可选择数据的加载
 */
export const useDataLoading = (initialData?: TaskBasic): UseDataLoadingReturn => {
  // 可选择的数据
  const [availableAlerts, setAvailableAlerts] = useState<TaskAlert[]>([]);
  const [availableDbConnections, setAvailableDbConnections] = useState<DBConnection[]>([]);
  const [availableAlertSends, setAvailableAlertSends] = useState<AlertSend[]>([]);
  const [availableOtherInfos, setAvailableOtherInfos] = useState<OtherInfo[]>([]);

  // 加载可选择的数据
  const loadData = async () => {
    try {
      const [alertsData, dbConnectionsData, alertSendsData, otherInfosData] = await Promise.all([
        TaskService.getAlerts(),
        TaskService.getDbConnections(),
        TaskService.getAlertSends(),
        TaskService.getOtherInfos(),
      ]);

      setAvailableAlerts(alertsData);
      setAvailableDbConnections(dbConnectionsData);
      setAvailableAlertSends(alertSendsData);
      setAvailableOtherInfos(otherInfosData);
    } catch (error) {
      message.error('加载数据失败:' + error);
    }
  };

  // 重新加载数据
  const reloadData = async () => {
    await loadData();
  };

  // 初始化加载数据
  useEffect(() => {
    loadData();
  }, [initialData]);

  return {
    availableAlerts,
    availableDbConnections,
    availableAlertSends,
    availableOtherInfos,
    reloadData,
  };
};
