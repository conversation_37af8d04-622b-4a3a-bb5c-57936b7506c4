import React, { useState } from 'react';
import { App, Tabs, Card } from 'antd';
import type { TabsProps } from 'antd';
import { AntdTableRefactored } from '../index';
import AntdTable from '../../AntdTable'; // 原始组件

/**
 * 原始组件与重构组件对比测试页面
 */
const ComparisonTest: React.FC = () => {
  const [activeTab, setActiveTab] = useState('refactored');

  const items: TabsProps['items'] = [
    {
      key: 'refactored',
      label: '重构后的组件',
      children: (
        <Card title="重构后的 AntdTable 组件" style={{ height: 'calc(100vh - 150px)' }}>
          <div style={{ height: 'calc(100vh - 200px)' }}>
            <AntdTableRefactored contentHeight={800} />
          </div>
        </Card>
      ),
    },
    {
      key: 'original',
      label: '原始组件',
      children: (
        <Card title="原始的 AntdTable 组件" style={{ height: 'calc(100vh - 150px)' }}>
          <div style={{ height: 'calc(100vh - 200px)' }}>
            <AntdTable contentHeight={800} />
          </div>
        </Card>
      ),
    },
  ];

  return (
    <App>
      <div style={{ height: '100vh', padding: '20px' }}>
        <h1 style={{ marginBottom: '20px', textAlign: 'center' }}>
          AntdTable 组件重构对比测试
        </h1>
        
        <div style={{ marginBottom: '20px', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
          <h3>重构说明：</h3>
          <ul>
            <li>✅ 将原始的 951 行单一组件拆分为多个小组件和 hooks</li>
            <li>✅ 保持所有原有功能：搜索、表格、分页、批量操作、编辑等</li>
            <li>✅ 提高代码可维护性和可复用性</li>
            <li>✅ 优化状态管理和性能</li>
            <li>✅ 完整的 TypeScript 类型支持</li>
          </ul>
        </div>

        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={items}
          size="large"
          style={{ height: 'calc(100vh - 150px)' }}
        />
      </div>
    </App>
  );
};

export default ComparisonTest;
