import React, { useState, useEffect } from 'react';
import { Form, Button, Space, Tabs } from 'antd';
import type { TabsProps } from 'antd';
import type { TaskBasic, TaskBasicFormSubmitData } from '../../types/task';

// 导入重构后的组件
import BasicInfoForm from './components/BasicInfoForm';
import AlertConfigTab from './components/AlertConfigTab';
import DatabaseConfigTab from './components/DatabaseConfigTab';
import OtherInfoTab from './components/OtherInfoTab';

// 导入自定义hooks
import { useFormData } from './hooks/useFormData';
import { useModalStates } from './hooks/useModalStates';
import { useDataLoading } from './hooks/useDataLoading';
import { useFormSubmit } from './hooks/useFormSubmit';

// 导入原有的Modal组件
import { AlertModal, DbConnectionModal, AlertSendModal } from '../TaskFormModals';
import { OtherInfoModal, SelectModal } from '../TaskFormModalsExtended';

interface ComplexTaskFormProps {
  initialData?: TaskBasic;
  onSubmit?: () => void;
  onCancel?: () => void;
  onReset?: () => void;
  isEdit?: boolean;
}

/**
 * 复杂任务表单组件（重构版）
 * 包含基本信息、告警配置、数据库连接、其他信息等标签页
 */
const ComplexTaskFormRefactored: React.FC<ComplexTaskFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  onReset,
}) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('basic');

  // 使用自定义hooks
  const {
    isEditMode,
    alerts,
    setAlerts,
    alertSends,
    setAlertSends,
    dbConnection,
    setDbConnection,
    otherInfo,
    setOtherInfo,
    initializeFormData,
  } = useFormData(initialData);

  const {
    alertModal,
    setAlertModal,
    alertSendModal,
    setAlertSendModal,
    dbConnectionModal,
    setDbConnectionModal,
    otherInfoModal,
    setOtherInfoModal,
    selectModal,
    setSelectModal,
  } = useModalStates();

  const {
    availableAlerts,
    availableDbConnections,
    availableAlertSends,
    availableOtherInfos,
    reloadData,
  } = useDataLoading(initialData);

  const { submitLoading, handleSubmit } = useFormSubmit({
    isEditMode,
    initialData,
    alerts,
    alertSends,
    dbConnection,
    otherInfo,
    onSubmit,
  });

  // 初始化表单数据
  useEffect(() => {
    initializeFormData(form);
  }, [initialData, form, initializeFormData]);

  // 告警相关操作
  const handleAddAlert = () => {
    setAlertModal({ visible: true, editingIndex: -1 });
  };

  const handleEditAlert = (index: number) => {
    setAlertModal({ visible: true, editingIndex: index });
  };

  const handleDeleteAlert = (index: number) => {
    const newAlerts = alerts.filter((_, i) => i !== index);
    setAlerts(newAlerts);
  };

  const handleSelectAlert = () => {
    setSelectModal({ visible: true, type: 'alert' });
  };

  // 告警发送相关操作
  const handleAddAlertSend = () => {
    setAlertSendModal({ visible: true, editingIndex: -1 });
  };

  const handleEditAlertSend = (index: number) => {
    setAlertSendModal({ visible: true, editingIndex: index });
  };

  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = alertSends.filter((_, i) => i !== index);
    setAlertSends(newAlertSends);
  };

  const handleSelectAlertSend = () => {
    setSelectModal({ visible: true, type: 'alertSend' });
  };

  // 数据库连接相关操作
  const handleAddDbConnection = () => {
    setDbConnectionModal({ visible: true });
  };

  const handleEditDbConnection = () => {
    setDbConnectionModal({ visible: true });
  };

  const handleDeleteDbConnection = () => {
    setDbConnection(null);
  };

  const handleSelectDbConnection = () => {
    setSelectModal({ visible: true, type: 'dbConnection' });
  };

  // 其他信息相关操作
  const handleAddOtherInfo = () => {
    setOtherInfoModal({ visible: true });
  };

  const handleEditOtherInfo = () => {
    setOtherInfoModal({ visible: true });
  };

  const handleDeleteOtherInfo = () => {
    setOtherInfo(null);
  };

  const handleSelectOtherInfo = () => {
    setSelectModal({ visible: true, type: 'otherInfo' });
  };

  // 定义Tabs的items配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'basic',
      label: '基本信息',
      children: <BasicInfoForm form={form} />,
    },
    {
      key: 'alert',
      label: '告警配置',
      children: (
        <AlertConfigTab
          alerts={alerts}
          alertSends={alertSends}
          onAddAlert={handleAddAlert}
          onEditAlert={handleEditAlert}
          onDeleteAlert={handleDeleteAlert}
          onSelectAlert={handleSelectAlert}
          onAddAlertSend={handleAddAlertSend}
          onEditAlertSend={handleEditAlertSend}
          onDeleteAlertSend={handleDeleteAlertSend}
          onSelectAlertSend={handleSelectAlertSend}
        />
      ),
    },
    {
      key: 'database',
      label: '数据库连接',
      children: (
        <DatabaseConfigTab
          dbConnection={dbConnection}
          onAddDbConnection={handleAddDbConnection}
          onEditDbConnection={handleEditDbConnection}
          onDeleteDbConnection={handleDeleteDbConnection}
          onSelectDbConnection={handleSelectDbConnection}
        />
      ),
    },
    {
      key: 'other',
      label: '其他信息',
      children: (
        <OtherInfoTab
          otherInfo={otherInfo}
          onAddOtherInfo={handleAddOtherInfo}
          onEditOtherInfo={handleEditOtherInfo}
          onDeleteOtherInfo={handleDeleteOtherInfo}
          onSelectOtherInfo={handleSelectOtherInfo}
        />
      ),
    },
  ];

  return (
    <div className='h-full flex flex-col'>
      <Form form={form} layout='vertical' onFinish={handleSubmit} className='flex-1'>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          className='flex-1'
          style={{ height: '100%' }}
        />

        {/* 底部操作按钮 */}
        <div className='flex justify-end gap-3 pt-4 border-t border-gray-200 bg-white'>
          <Space>
            <Button onClick={onCancel}>取消</Button>
            <Button onClick={onReset}>重置</Button>
            <Button type='primary' htmlType='submit' loading={submitLoading}>
              {isEditMode ? '更新' : '创建'}
            </Button>
          </Space>
        </div>
      </Form>

      {/* Modal组件 */}
      <AlertModal
        visible={alertModal.visible}
        editingData={alertModal.editingIndex >= 0 ? alerts[alertModal.editingIndex] : undefined}
        onCancel={() => setAlertModal({ visible: false, editingIndex: -1 })}
        onSubmit={(data) => {
          if (alertModal.editingIndex >= 0) {
            const newAlerts = [...alerts];
            newAlerts[alertModal.editingIndex] = data;
            setAlerts(newAlerts);
          } else {
            setAlerts([...alerts, data]);
          }
          setAlertModal({ visible: false, editingIndex: -1 });
        }}
      />

      <AlertSendModal
        visible={alertSendModal.visible}
        editingData={alertSendModal.editingIndex >= 0 ? alertSends[alertSendModal.editingIndex] : undefined}
        onCancel={() => setAlertSendModal({ visible: false, editingIndex: -1 })}
        onSubmit={(data) => {
          if (alertSendModal.editingIndex >= 0) {
            const newAlertSends = [...alertSends];
            newAlertSends[alertSendModal.editingIndex] = data;
            setAlertSends(newAlertSends);
          } else {
            setAlertSends([...alertSends, data]);
          }
          setAlertSendModal({ visible: false, editingIndex: -1 });
        }}
      />

      <DbConnectionModal
        visible={dbConnectionModal.visible}
        editingData={dbConnection}
        onCancel={() => setDbConnectionModal({ visible: false })}
        onSubmit={(data) => {
          setDbConnection(data);
          setDbConnectionModal({ visible: false });
        }}
      />

      <OtherInfoModal
        visible={otherInfoModal.visible}
        editingData={otherInfo}
        onCancel={() => setOtherInfoModal({ visible: false })}
        onSubmit={(data) => {
          setOtherInfo(data);
          setOtherInfoModal({ visible: false });
        }}
      />

      <SelectModal
        visible={selectModal.visible}
        type={selectModal.type}
        data={{
          alert: availableAlerts,
          alertSend: availableAlertSends,
          dbConnection: availableDbConnections,
          otherInfo: availableOtherInfos,
        }}
        onCancel={() => setSelectModal({ visible: false, type: '' })}
        onSubmit={(selectedData) => {
          if (selectModal.type === 'alert') {
            setAlerts([...alerts, ...selectedData]);
          } else if (selectModal.type === 'alertSend') {
            setAlertSends([...alertSends, ...selectedData]);
          } else if (selectModal.type === 'dbConnection' && selectedData.length > 0) {
            setDbConnection(selectedData[0]);
          } else if (selectModal.type === 'otherInfo' && selectedData.length > 0) {
            setOtherInfo(selectedData[0]);
          }
          setSelectModal({ visible: false, type: '' });
        }}
      />
    </div>
  );
};

export default ComplexTaskFormRefactored;
