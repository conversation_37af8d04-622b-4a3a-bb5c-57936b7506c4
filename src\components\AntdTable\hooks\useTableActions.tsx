import React, { useCallback } from 'react';
import { Modal, App } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { TaskService } from '../../../services/taskService';
import type { TaskBasic } from '../../../types/task';

interface UseTableActionsProps {
  /** 重新加载数据 */
  loadData: () => Promise<void>;
  /** 清空选择状态 */
  clearSelection: () => void;
  /** 所有选中的行数据 */
  allSelectedRows: Map<React.Key, TaskBasic>;
}

interface UseTableActionsReturn {
  /** 删除单个任务 */
  handleDelete: (id: number) => Promise<void>;
  /** 批量删除任务 */
  handleBatchDelete: () => Promise<void>;
}

/**
 * 表格操作管理Hook
 * 管理表格的删除等操作
 */
export const useTableActions = ({
  loadData,
  clearSelection,
  allSelectedRows,
}: UseTableActionsProps): UseTableActionsReturn => {
  const { message } = App.useApp();

  // 删除单个任务
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        const res = await TaskService.deleteTask(id);
        message.success(`成功删除任务数量 ${res.total}`);
        loadData();
      } catch (error) {
        message.error('删除失败');
        console.error('删除失败:', error);
      }
    },
    [loadData, message]
  );

  // 批量删除任务
  const handleBatchDelete = useCallback(async () => {
    const totalSelected = allSelectedRows.size;
    if (totalSelected === 0) {
      message.warning('请先选择要删除的任务');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${totalSelected} 个任务吗？`,
      icon: <ExclamationCircleOutlined />,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const ids = Array.from(allSelectedRows.keys()).map(key => Number(key));
          const res = await TaskService.batchDeleteTasks(ids);
          message.success(`成功删除任务数量 ${res.total}`);
          // 清空所有选择状态
          clearSelection();
          loadData();
        } catch (error) {
          message.error('批量删除失败');
          console.error('批量删除失败:', error);
        }
      },
    });
  }, [allSelectedRows, loadData, message, clearSelection]);

  return {
    handleDelete,
    handleBatchDelete,
  };
};
