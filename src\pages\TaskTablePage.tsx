import React from 'react';
import { useOutletContext } from 'react-router-dom';
// import AntdTable from '../components/AntdTable';
import AntdTableRefactored from '@/components/AntdTable/AntdTableRefactored';

interface OutletContext {
  contentHeight: number;
}

/**
 * 任务管理页面
 * 展示完整的任务管理表格功能
 */
const TaskTablePage: React.FC = () => {
  const { contentHeight } = useOutletContext<OutletContext>();
  // AntdTableRefactored
  // return <AntdTable contentHeight={contentHeight} />;
  return <AntdTableRefactored contentHeight={contentHeight} />;
};

export default TaskTablePage;
