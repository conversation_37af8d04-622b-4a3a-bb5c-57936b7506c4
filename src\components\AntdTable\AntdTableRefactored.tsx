import React, { useCallback, useEffect, useState } from 'react';
import { But<PERSON>, Drawer, Form, App } from 'antd';
import { EditOutlined, PlusOutlined, TeamOutlined } from '@ant-design/icons';
import type { TaskBasic, TaskDrawerState, TaskBasicSearchParams } from '../../types/task';
import styles from '../AntdTable.module.css';
import ComplexTaskForm from '../ComplexTaskForm';
import GroupManagementModal from '../GroupManagementModal';

// 导入重构后的组件
import SearchForm from './components/SearchForm';
import AdvancedSearchModal from './components/AdvancedSearchModal';
import TaskTable from './components/TaskTable';
import BatchOperations from './components/BatchOperations';
import TablePagination from './components/TablePagination';

// 导入自定义hooks
import { useTableData } from './hooks/useTableData';
import { useTableSelection } from './hooks/useTableSelection';
import { useTableSearch } from './hooks/useTableSearch';
import { useTableActions } from './hooks/useTableActions';

// 导入工具函数
import { calculateTableScrollY } from './utils';
import { ComplexTaskFormRefactored } from '../ComplexTaskForm';

interface AntdTableProps {
  contentHeight?: number;
}

/**
 * 任务管理表格组件（重构版）
 * 包含查询、新增、编辑、删除、分页等完整功能
 */
const AntdTableRefactored: React.FC<AntdTableProps> = ({ contentHeight }) => {
  const { message } = App.useApp();
  const [tableScrollY, setTableScrollY] = useState<number>(616);

  // 抽屉状态
  const [editDrawer, setEditDrawer] = useState<TaskDrawerState>({
    visible: false,
  });
  const [currentRecord, setCurrentRecord] = useState<TaskBasic | null>(null);

  // 分组管理Modal状态
  const [groupManagementModal, setGroupManagementModal] = useState({
    visible: false,
  });

  // 表单实例
  const [searchForm] = Form.useForm();

  // 使用自定义hooks
  const { searchParams, setSearchParams, sortedInfo, setSortedInfo, filteredInfo, setFilteredInfo, searchModal, setSearchModal, handleTableChange, resetSearchState } = useTableSearch();

  const { data, loading, total, pagination, setPagination, loadData } = useTableData(searchParams);

  const { allSelectedRows, rowSelection, clearSelection } = useTableSelection(data);

  const { handleDelete, handleBatchDelete } = useTableActions({
    loadData,
    clearSelection,
    allSelectedRows,
  });

  // 根据传入的contentHeight计算表格滚动高度
  useEffect(() => {
    const calculatedScrollY = calculateTableScrollY(contentHeight);
    setTableScrollY(calculatedScrollY);
  }, [contentHeight]);

  // 初始化加载数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 搜索表单提交处理
  const handleSearchFormSubmit = useCallback(
    (values: TaskBasicSearchParams) => {
      console.log('搜索表单数据:', values);

      // 更新搜索参数状态
      setSearchParams(values);

      // 重置排序和筛选状态
      setSortedInfo({});
      setFilteredInfo({});

      // 重置选择状态
      clearSelection();

      // 使用搜索参数加载数据
      loadData({
        ...values,
      });
    },
    [loadData, setSearchParams, setSortedInfo, setFilteredInfo, clearSelection]
  );

  // 重置搜索
  const handleReset = useCallback(() => {
    // 重置搜索参数
    setSearchParams({});
    // 重置分页
    setPagination({
      current: 1,
      page_size: 15,
    });
    // 重置排序和筛选状态
    resetSearchState();
    // 重置时清空选择状态
    clearSelection();
    // 重置搜索表单
    searchForm.resetFields();
  }, [searchForm, setSearchParams, setPagination, resetSearchState, clearSelection]);

  // 详细查询提交
  const handleAdvancedSearch = useCallback(
    async (values: TaskBasicSearchParams) => {
      setSearchParams(values);
      setPagination(prev => ({
        ...prev,
        current: 1,
      }));
      // 重置排序和筛选状态
      setSortedInfo({});
      setFilteredInfo({});
      setSearchModal({
        visible: false,
      });
      // 详细查询时清空选择状态
      clearSelection();
      // 立即使用第一页和新的搜索条件加载数据
      loadData({
        current: 1,
        ...values,
      });
    },
    [loadData, setSearchParams, setPagination, setSortedInfo, setFilteredInfo, setSearchModal, clearSelection]
  );

  // 编辑处理
  const handleEdit = useCallback((record: TaskBasic) => {
    setCurrentRecord(record);
    setEditDrawer({
      visible: true,
      loading: false,
    });
  }, []);

  // 抽屉关闭处理
  const handleDrawerClose = useCallback(() => {
    setEditDrawer({ visible: false });
    setCurrentRecord(null);
  }, []);

  // 分页变化处理
  const handlePaginationChange = useCallback(
    (page: number, pageSize: number) => {
      const newPageSize = pageSize || 10;
      const newPagination = {
        current: page,
        page_size: newPageSize,
      };

      // 先更新分页状态
      setPagination(newPagination);

      // 立即使用新的分页参数加载数据
      loadData({
        current: page,
        page_size: newPageSize,
      });
    },
    [setPagination, loadData]
  );

  return (
    <div className='h-full flex flex-col'>
      {/* 主要内容区域  */}
      <div className={styles.mainContainer}>
        {/* 快速搜索表单区域 */}
        <div className={styles.searchSection}>
          <SearchForm form={searchForm} onSearch={handleSearchFormSubmit} onReset={handleReset} onAdvancedSearch={() => setSearchModal({ visible: true })} />
        </div>

        {/* ROW区域 - 新增任务和批量操作 */}
        <div className='flex-shrink-0 h-12 flex items-center justify-between px-4 bg-white border-t border-gray-200'>
          {/* 左侧：新增任务按钮和分组管理按钮 */}
          <div className='flex items-center gap-2'>
            <Button
              type='primary'
              size='middle'
              icon={<PlusOutlined />}
              onClick={() => {
                // 清空当前记录
                setCurrentRecord(null);
                setEditDrawer({
                  visible: true,
                });
              }}
              className={`${styles.addTaskBtn} w-40 h-8 flex items-center justify-center`}
            >
              新增任务
            </Button>
            <Button
              type='primary'
              size='middle'
              icon={<TeamOutlined />}
              onClick={() => {
                setGroupManagementModal({
                  visible: true,
                });
              }}
              className={`${styles.groupManageBtn} w-40 h-8 flex items-center justify-center`}
            >
              分组管理
            </Button>
          </div>

          {/* 右侧：批量操作区域 - 当有选中项时显示 */}
          <BatchOperations allSelectedRows={allSelectedRows} onBatchDelete={handleBatchDelete} onCancelSelection={clearSelection} styles={styles} />
        </div>

        {/* 表格区域 */}
        <TaskTable
          data={data}
          loading={loading}
          scrollY={tableScrollY}
          sortedInfo={sortedInfo}
          filteredInfo={filteredInfo}
          rowSelection={rowSelection}
          onChange={handleTableChange}
          onEdit={handleEdit}
          onDelete={handleDelete}
          styles={styles}
        />

        {/* 分页区域 - 固定在表格底部 */}
        <TablePagination current={pagination.current} pageSize={pagination.page_size} total={total} onChange={handlePaginationChange} styles={styles} />
      </div>

      {/* 详细查询Modal */}
      <AdvancedSearchModal
        visible={searchModal.visible}
        form={searchForm}
        initialValues={searchParams}
        onSearch={handleAdvancedSearch}
        onCancel={() => setSearchModal({ visible: false })}
        onReset={() => {
          searchForm.resetFields();
          setSearchParams({});
          setSearchModal({ visible: false });
        }}
      />

      {/* 编辑抽屉 */}
      <Drawer
        title={
          <div className='flex items-center gap-2'>
            <EditOutlined className='text-blue-600' />
            <span className='text-lg font-semibold'>{currentRecord ? '编辑任务' : '新增任务'}</span>
          </div>
        }
        width='90%'
        open={editDrawer.visible}
        onClose={handleDrawerClose}
        maskClosable={false}
        className='custom-drawer'
        footer={null}
      >
        <ComplexTaskFormRefactored
          initialData={currentRecord || undefined}
          onSubmit={() => {
            // 表单提交成功后的回调
            setEditDrawer({ visible: false, loading: false });
            setCurrentRecord(null);
            loadData(); // 重新加载数据
          }}
          onCancel={handleDrawerClose}
          onReset={() => {
            // 重置操作的回调
            searchForm.resetFields();
            message.info('表单已重置');
          }}
        />
      </Drawer>

      {/* 分组管理Modal */}
      <GroupManagementModal visible={groupManagementModal.visible} onCancel={() => setGroupManagementModal({ visible: false })} />
    </div>
  );
};

export default AntdTableRefactored;
