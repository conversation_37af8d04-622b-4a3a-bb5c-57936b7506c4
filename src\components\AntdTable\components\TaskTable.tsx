import React from 'react';
import { Table } from 'antd';
import type { TableProps } from 'antd';
import type { FilterValue, SorterResult, TableRowSelection } from 'antd/es/table/interface';
import type { TaskBasic } from '../../../types/task';
import { createTableColumns } from '../utils/tableColumns';

interface TaskTableProps {
  /** 表格数据 */
  data: TaskBasic[];
  /** 加载状态 */
  loading: boolean;
  /** 表格滚动高度 */
  scrollY: number;
  /** 排序信息 */
  sortedInfo: SorterResult<TaskBasic> | SorterResult<TaskBasic>[];
  /** 筛选信息 */
  filteredInfo: Record<string, FilterValue | null>;
  /** 行选择配置 */
  rowSelection?: TableRowSelection<TaskBasic>;
  /** 表格变化回调 */
  onChange: TableProps<TaskBasic>['onChange'];
  /** 编辑回调 */
  onEdit: (record: TaskBasic) => void;
  /** 删除回调 */
  onDelete: (id: number) => void;
  /** 样式对象 */
  styles: any;
  /** 自定义类名 */
  className?: string;
}

/**
 * 任务表格组件
 * 封装了表格的基本功能，包括排序、筛选、分页等
 */
const TaskTable: React.FC<TaskTableProps> = ({
  data,
  loading,
  scrollY,
  sortedInfo,
  filteredInfo,
  rowSelection,
  onChange,
  onEdit,
  onDelete,
  styles,
  className = 'custom-table h-full',
}) => {
  // 创建表格列配置
  const columns = createTableColumns(
    sortedInfo,
    filteredInfo,
    onEdit,
    onDelete,
    styles
  );

  return (
    <div className='flex-1 overflow-hidden'>
      <div className='flex-1 overflow-hidden min-h-0'>
        <Table
          columns={columns}
          dataSource={data}
          rowKey='id'
          loading={loading}
          pagination={false}
          rowSelection={rowSelection}
          onChange={onChange}
          scroll={{
            x: 1200,
            y: scrollY,
          }}
          size='middle'
          className={className}
        />
      </div>
    </div>
  );
};

export default TaskTable;
