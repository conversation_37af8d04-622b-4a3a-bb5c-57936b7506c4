/**
 * 计算表格滚动高度
 * @param contentHeight 内容区域高度
 * @param minHeight 最小高度，默认300px
 * @returns 计算后的滚动高度
 */
export const calculateTableScrollY = (contentHeight?: number, minHeight: number = 300): number => {
  if (!contentHeight) {
    return 616; // 默认高度
  }
  
  const calculatedScrollY = contentHeight - 192; // 减去搜索区域、按钮区域、分页区域等
  return Math.max(calculatedScrollY, minHeight);
};

/**
 * 格式化频率显示
 * @param frequency 频率对象或字符串
 * @returns 格式化后的字符串
 */
export const formatFrequency = (frequency: string | { value: number; unit: string }): string => {
  if (frequency && typeof frequency === 'object') {
    return `${frequency.value}${frequency.unit}`;
  }
  return frequency as string;
};

/**
 * 格式化星期显示
 * @param weekday 星期数组或字符串
 * @returns 格式化后的字符串
 */
export const formatWeekday = (weekday: string | string[]): string => {
  if (Array.isArray(weekday)) {
    return weekday.join(',');
  }
  return weekday;
};

/**
 * 比较频率用于排序
 * @param a 频率A
 * @param b 频率B
 * @returns 比较结果
 */
export const compareFrequency = (
  a: string | { value: number; unit: string },
  b: string | { value: number; unit: string }
): number => {
  const aFreq = typeof a === 'object' ? `${a.value}${a.unit}` : a;
  const bFreq = typeof b === 'object' ? `${b.value}${b.unit}` : b;
  return aFreq.toString().localeCompare(bFreq.toString());
};

/**
 * 比较星期用于排序
 * @param a 星期A
 * @param b 星期B
 * @returns 比较结果
 */
export const compareWeekday = (a: string | string[], b: string | string[]): number => {
  const aWeekday = Array.isArray(a) ? a.join(',') : a;
  const bWeekday = Array.isArray(b) ? b.join(',') : b;
  return aWeekday.localeCompare(bWeekday);
};
